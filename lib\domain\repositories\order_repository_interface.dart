import 'package:dio/dio.dart';
import 'package:rozana/domain/entities/order_item_entity.dart';
import '../entities/order_entity.dart';

abstract class OrderRepositoryInterface {
  /// Creates a new order with the provided data
  Future<Response?> createOrder({
    required num totalAmount,
    required List<Map<String, dynamic>> items,
    Map<String, dynamic>? address,
    String? paymentMethod,
    Map<String, dynamic>? paymentDetails,
    String? facilityName,
    String? estimatedDeliveryTime,
  });

  /// Get order history for a customer with pagination and filtering
  Future<List<OrderEntity>> getOrderHistory({
    String customerId = 'customer_123',
    String status = '',
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
  });

  Future<List<dynamic>> getPreviousOrders({
    String customerId = 'customer_123',
    String status = '',
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
  });

  /// Get detailed information for a specific order
  Future<OrderEntity?> getOrderDetails(String orderId);

  /// Cancel an order
  Future<bool> cancelOrder(String orderId);

  Future<void> returnOrder(String orderId, OrderItemEntity item, String reason);

  /// Get SKU data from TypeSense for a list of SKUs
  Future<List<dynamic>> getSKUDataFromTS(List<dynamic> skuList);

  /// Verify Razorpay payment after successful payment
  Future<Response?> verifyPayment({
    required String razorpayOrderId,
    required String razorpayPaymentId,
    required String razorpaySignature,
    required String omsOrderId,
  });
}
