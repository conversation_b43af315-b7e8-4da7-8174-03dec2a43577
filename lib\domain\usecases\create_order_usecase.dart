import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/services/appflyer_services/appflyer_events.dart';
import 'package:rozana/data/models/cart_item_model.dart';
import 'package:rozana/data/models/adress_model.dart';
import 'package:rozana/data/models/payment_method_model.dart';
import 'package:rozana/data/models/order_payment_model.dart';
import 'package:rozana/domain/repositories/order_repository_interface.dart';
import 'package:rozana/features/address/bloc/address_bloc.dart';
import 'package:dio/dio.dart';

/// Use case for creating orders with reusable business logic
/// Handles order creation for all payment methods and scenarios
class CreateOrderUseCase {
  final OrderRepositoryInterface _orderRepository;

  CreateOrderUseCase(this._orderRepository);

  /// Main method to create an order
  /// Returns CreateOrderResult with success/failure status and data
  Future<CreateOrderResult> call(
      CreateOrderParams params, String source) async {
    try {
      // Validate input parameters
      final validationResult = _validateOrderParams(params);
      if (!validationResult.isValid) {
        return CreateOrderResult.failure(validationResult.error!);
      }

      // Format items for API
      final formattedItems = _formatItemsForApi(params.items);

      // Calculate total amount with fees and taxes
      final totalAmount = _calculateTotalAmount(
        params.items,
        params.baseTotal,
        params.deliveryFee,
        params.tax,
        params.discount,
      );

      // Prepare order creation request with payment details
      Map<String, dynamic>? additionalData = {
        'is_approved': false,
      };

      // Handle multiple payment methods if available
      if (params.paymentMethods != null && params.paymentMethods!.isNotEmpty) {
        final List<Map<String, dynamic>> payments = params.paymentMethods!
            .map((method) => OrderPaymentModel(
                  paymentMode: method.type,
                  amount: source == 'warehouse' ? totalAmount : method.amount,
                  createPaymentOrder:
                      (method.type == 'razorpay') || (method.type == 'wallet'),
                  metadata: method.metadata,
                ).toJson())
            .toList();
        additionalData['payment'] = payments;
      } else {
        // Fallback to single payment method for backward compatibility
        additionalData['payment'] = [
          {
            'payment_mode': params.paymentMethod,
            'create_payment_order': (params.paymentMethod == 'razorpay') ||
                    (params.paymentMethod == 'wallet')
                ? true
                : false,
            'amount': totalAmount,
          },
        ];
      }

      // Create order via repository
      // Get facility name from the first item in the cart if available
      final String? facilityName =
          params.items.isNotEmpty ? params.items.first.facilityName : null;

      // Calculate estimated delivery time
      final String? estimatedDeliveryTime = _calculateEstimatedDeliveryTime();

      final response = await _orderRepository.createOrder(
        totalAmount: totalAmount,
        items: formattedItems,
        address: params.deliveryAddress?.toJson(),
        paymentMethod: params.paymentMethod,
        paymentDetails: additionalData,
        facilityName: facilityName,
        estimatedDeliveryTime: estimatedDeliveryTime,
      );

      // Handle response
      if (response?.statusCode == 200 || response?.statusCode == 201) {
        return await _handleOrderSuccess(
          response: response!,
          params: params,
          totalAmount: totalAmount,
          formattedItems: formattedItems,
        );
      } else {
        return await _handleOrderFailure(
          response: response,
          params: params,
          totalAmount: totalAmount,
        );
      }
    } catch (e) {
      debugPrint('CreateOrderUseCase error: $e');

      // Log failed purchase event
      await _logFailedPurchaseEvent(
        params.items,
        params.baseTotal,
        e.toString(),
      );

      return CreateOrderResult.failure('Failed to create order: $e');
    }
  }

  /// Validates order parameters
  OrderValidationResult _validateOrderParams(CreateOrderParams params) {
    if (params.items.isEmpty) {
      return OrderValidationResult.invalid('Cart is empty');
    }

    if (params.paymentMethod.isEmpty) {
      return OrderValidationResult.invalid('Payment method is required');
    }

    // Validate items have required fields
    for (final item in params.items) {
      if (item.skuID == null || item.skuID!.isEmpty) {
        return OrderValidationResult.invalid('Item SKU is required');
      }
      if ((item.quantity ?? 0) <= 0) {
        return OrderValidationResult.invalid(
            'Item quantity must be greater than 0');
      }
      // if ((item.price ?? 0) <= 0) {
      //   return OrderValidationResult.invalid(
      //       'Item price must be greater than 0');
      // }
    }

    return OrderValidationResult.valid();
  }

  /// Formats cart items for API consumption
  List<Map<String, dynamic>> _formatItemsForApi(List<CartItemModel> items) {
    return items
        .map((item) => {
              'sku': item.skuID ?? 'ITEM-${item.id}',
              'quantity': item.quantity ?? 0,
              'unit_price': item.price ?? 0,
              'tax': item.tax ?? 0,
              'sale_price':
                  (item.discountedPrice != null && item.discountedPrice! > 0)
                      ? item.discountedPrice!
                      : item.price ?? 0,
            })
        .toList();
  }

  /// Calculates total amount including fees and taxes
  double _calculateTotalAmount(
    List<CartItemModel> items,
    double baseTotal,
    double deliveryFee,
    double tax,
    double discount,
  ) {
    double total = baseTotal;

    // If base total is zero or negative, calculate from items
    if (total <= 0 && items.isNotEmpty) {
      total = items.fold(0.0, (sum, item) {
        final itemPrice = item.price ?? 0;
        final quantity = item.quantity ?? 1;
        return sum + (itemPrice * quantity);
      });
    }

    // Add delivery fee
    total += deliveryFee;

    // Add tax
    total += tax;

    // Subtract discount
    total -= discount;

    return double.parse(total.toStringAsFixed(2));
  }

  /// Handles successful order creation
  Future<CreateOrderResult> _handleOrderSuccess({
    required Response response,
    required CreateOrderParams params,
    required double totalAmount,
    required List<Map<String, dynamic>> formattedItems,
  }) async {
    // Generate order ID from response or create locally
    final orderId = response.data['order_id'] ??
        'ORD-${DateTime.now().millisecondsSinceEpoch}';

    // Create order data for response
    final orderData = {
      'orderId': orderId,
      'items': params.items.map((item) => item.toJson()).toList(),
      'paymentMethod': params.paymentMethod,
      'total': totalAmount,
      'subtotal': params.baseTotal,
      'tax': params.tax,
      'discount': params.discount,
      'deliveryFee': params.deliveryFee,
      'itemCount': params.items
          .fold<int>(0, (sum, item) => sum + (item.quantity?.toInt() ?? 0)),
      'orderDate': DateTime.now().toIso8601String(),
    };

    // Add address if available
    if (params.deliveryAddress != null) {
      orderData['address'] = params.deliveryAddress!.toJson();
    }

    // Check if this order requires online payment
    bool hasRazorpayPayment = false;

    // Check in multiple payment methods first
    if (params.paymentMethods != null && params.paymentMethods!.isNotEmpty) {
      hasRazorpayPayment = params.paymentMethods!
          .any((method) => method.type.toLowerCase() == 'razorpay');
    } else {
      // Fallback to single payment method
      hasRazorpayPayment = params.paymentMethod.toLowerCase() == 'razorpay';
    }

    final paymentOrderDetails = response.data['payment_order_details'];

    bool hasValidRazorpayDetails = false;

    if (paymentOrderDetails is List && paymentOrderDetails.isNotEmpty) {
      for (final item in paymentOrderDetails) {
        if (item is Map<String, dynamic> && item['razorpay_order_id'] != null) {
          hasValidRazorpayDetails = true;
          break;
        }
      }
    }

    final requiresPayment = hasRazorpayPayment && hasValidRazorpayDetails;

    if (!requiresPayment) {
      // For COD orders, log successful purchase event immediately
      await _logPurchaseEvent(
        params.items,
        totalAmount,
        orderId,
      );
    }

    return CreateOrderResult.success(
      orderId: orderId,
      orderData: orderData,
      totalAmount: totalAmount,
      paymentOrderDetails: paymentOrderDetails,
      requiresPayment: requiresPayment,
    );
  }

  /// Handles failed order creation
  Future<CreateOrderResult> _handleOrderFailure({
    required Response? response,
    required CreateOrderParams params,
    required double totalAmount,
  }) async {
    final errorMessage = response?.statusMessage ?? 'Unknown error';

    // Log failed purchase event
    await _logFailedPurchaseEvent(
      params.items,
      totalAmount,
      errorMessage,
    );

    return CreateOrderResult.failure(
      'Failed to create order: $errorMessage',
    );
  }

  /// Logs successful purchase event to analytics
  Future<void> _logPurchaseEvent(
    List<CartItemModel> items,
    double totalAmount,
    String orderId,
  ) async {
    try {
      final userId = await _getCurrentUserId();
      await AppsFlyerEvents.purchase(
        cartItems: items,
        revenue: totalAmount,
        orderId: orderId,
        userId: userId,
      );
    } catch (e) {
      debugPrint('Failed to log purchase event: $e');
    }
  }

  /// Logs failed purchase event to analytics
  Future<void> _logFailedPurchaseEvent(
    List<CartItemModel> items,
    double totalAmount,
    String errorMessage,
  ) async {
    try {
      final userId = await _getCurrentUserId();
      await AppsFlyerEvents.failedPurchase(
        cartItems: items,
        price: totalAmount,
        userId: userId,
        errorMessage: errorMessage,
      );
    } catch (e) {
      debugPrint('Failed to log failed purchase event: $e');
    }
  }

  /// Gets current user ID from stored preferences
  Future<String> _getCurrentUserId() async {
    try {
      final userDataJson = AppPreferences.getUserdata();
      if (userDataJson != null && userDataJson.isNotEmpty) {
        final userData = jsonDecode(userDataJson);
        return userData['uid'] ?? 'CUST-001';
      }
    } catch (e) {
      debugPrint('Error getting user ID: $e');
    }
    return 'CUST-001'; // Default fallback
  }

  /// Calculate estimated delivery time in ISO 8601 format
  /// Returns current time + delivery duration from AddressBloc
  String? _calculateEstimatedDeliveryTime() {
    try {
      // Get delivery duration from AddressBloc
      final addressBloc = getIt<AddressBloc>();
      final deliveryDuration = addressBloc.state.delliveryDuration;

      debugPrint(
          'CreateOrderUseCase: Delivery duration from AddressBloc: "$deliveryDuration"');

      if (deliveryDuration == null ||
          deliveryDuration.isEmpty ||
          deliveryDuration == '...') {
        debugPrint(
            'CreateOrderUseCase: Delivery duration is null, empty, or "..." - not calculating ETA');
        return null;
      }

      // Parse delivery duration and convert to minutes
      final durationInMinutes = _parseDurationToMinutes(deliveryDuration);
      debugPrint(
          'CreateOrderUseCase: Parsed duration in minutes: $durationInMinutes');

      if (durationInMinutes == null) {
        debugPrint(
            'CreateOrderUseCase: Failed to parse duration - not calculating ETA');
        return null;
      }

      // Calculate estimated delivery time: current time + duration
      final now = DateTime.now().toUtc();
      final estimatedDeliveryTime =
          now.add(Duration(minutes: durationInMinutes));

      final isoString = estimatedDeliveryTime.toIso8601String();
      debugPrint('CreateOrderUseCase: Calculated ETA: $isoString');

      // Return in ISO 8601 format with milliseconds and Z suffix
      return isoString;
    } catch (e) {
      debugPrint('Error calculating estimated delivery time: $e');
      return null;
    }
  }

  /// Parse duration string to minutes
  /// Supports formats like "30 mins", "1 hr 30 mins", "2 hours", etc.
  int? _parseDurationToMinutes(String durationText) {
    try {
      final durationRegExp = RegExp(
          r'(?:(\d+)\s*(?:day[s]?|d))?\s*(?:(\d+)\s*(?:hour[s]?|hr[s]?|h))?\s*(?:(\d+)\s*(?:minute[s]?|min[s]?|m))?',
          caseSensitive: false);

      final match = durationRegExp.firstMatch(durationText.toLowerCase());

      if (match == null) return null;

      final days = int.tryParse(match.group(1) ?? '0') ?? 0;
      final hours = int.tryParse(match.group(2) ?? '0') ?? 0;
      final minutes = int.tryParse(match.group(3) ?? '0') ?? 0;

      // Convert everything to minutes
      return (days * 24 * 60) + (hours * 60) + minutes;
    } catch (e) {
      debugPrint('Error parsing duration: $e');
      return null;
    }
  }
}

/// Parameters for creating an order
class CreateOrderParams {
  final List<CartItemModel> items;
  final String
      paymentMethod; // Primary payment method (for backward compatibility)
  final List<PaymentMethodModel>?
      paymentMethods; // New field for multiple payment methods
  final AddressModel? deliveryAddress;
  final double baseTotal;
  final double deliveryFee;
  final double tax;
  final double discount;
  final Map<String, dynamic>? paymentDetails;

  const CreateOrderParams({
    required this.items,
    required this.paymentMethod,
    this.paymentMethods,
    this.deliveryAddress,
    required this.baseTotal,
    this.deliveryFee = 0.0,
    this.tax = 0.0,
    this.discount = 0.0,
    this.paymentDetails,
  });
}

/// Result of order creation operation
class CreateOrderResult {
  final bool isSuccess;
  final String? error;
  final String? orderId;
  final Map<String, dynamic>? orderData;
  final double? totalAmount;
  final dynamic
      paymentOrderDetails; // Changed to dynamic to handle both Map and List
  final bool requiresPayment;

  const CreateOrderResult._({
    required this.isSuccess,
    this.error,
    this.orderId,
    this.orderData,
    this.totalAmount,
    this.paymentOrderDetails,
    this.requiresPayment = false,
  });

  factory CreateOrderResult.success({
    required String orderId,
    required Map<String, dynamic> orderData,
    required double totalAmount,
    dynamic paymentOrderDetails,
    bool requiresPayment = false,
  }) {
    return CreateOrderResult._(
      isSuccess: true,
      orderId: orderId,
      orderData: orderData,
      totalAmount: totalAmount,
      paymentOrderDetails: paymentOrderDetails,
      requiresPayment: requiresPayment,
    );
  }

  factory CreateOrderResult.failure(String error) {
    return CreateOrderResult._(
      isSuccess: false,
      error: error,
    );
  }

  /// Get Razorpay order ID for payment initiation
  String? get razorpayPaymentOrderId {
    try {
      if (paymentOrderDetails is Map<String, dynamic>) {
        return paymentOrderDetails?['payment_order_id'];
      } else if (paymentOrderDetails is List &&
          paymentOrderDetails.isNotEmpty) {
        for (final item in paymentOrderDetails) {
          if (item is Map<String, dynamic>) {
            if (item['payment_mode']?.toString().toLowerCase() == 'razorpay') {
              return item['payment_order_id'];
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error getting razorpayOrderId: $e');
    }
    return null;
  }

  /// Get Razorpay key ID
  String? get razorpayKeyId {
    try {
      if (paymentOrderDetails is Map<String, dynamic>) {
        return paymentOrderDetails?['razorpay_key_id'];
      } else if (paymentOrderDetails is List &&
          paymentOrderDetails.isNotEmpty) {
        for (final item in paymentOrderDetails) {
          if (item is Map<String, dynamic>) {
            if (item['payment_mode']?.toString().toLowerCase() == 'razorpay') {
              return item['razorpay_key_id'];
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error getting razorpayKeyId: $e');
    }
    return null;
  }
}

/// Result of order validation
class OrderValidationResult {
  final bool isValid;
  final String? error;

  const OrderValidationResult._({
    required this.isValid,
    this.error,
  });

  factory OrderValidationResult.valid() {
    return const OrderValidationResult._(isValid: true);
  }

  factory OrderValidationResult.invalid(String error) {
    return OrderValidationResult._(isValid: false, error: error);
  }
}
